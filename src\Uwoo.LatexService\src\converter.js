/**
 * LaTeX转换器核心模块
 * 支持LaTeX到SVG/MathML/PNG的转换
 */

const mathjax = require('mathjax-node');
const svg2png = require('mathjax-node-svg2png');
const sharp = require('sharp');
const config = require('./config');

class LatexConverter {
    constructor(logger) {
        this.logger = logger;
        this.cache = new Map();
        this.initMathJax();
    }

    /**
     * 初始化MathJax
     */
    initMathJax() {
        mathjax.config({
            MathJax: config.mathjax
        });

        mathjax.start();
        this.logger.info('MathJax初始化完成');
    }

    /**
     * 验证LaTeX代码安全性
     */
    validateLatex(latex) {
        // 检查长度
        if (latex.length > config.security.maxLatexLength) {
            throw new Error(`LaTeX代码长度超过限制: ${config.security.maxLatexLength}`);
        }

        // 检查危险命令
        for (const blocked of config.security.blockedCommands) {
            if (latex.includes(`\\${blocked}`)) {
                throw new Error(`包含禁用命令: \\${blocked}`);
            }
        }

        return true;
    }

    /**
     * 生成缓存键
     */
    getCacheKey(latex, format, options) {
        return `${latex}_${format}_${JSON.stringify(options)}`;
    }

    /**
     * 从缓存获取结果
     */
    getFromCache(key) {
        if (!config.cache.enabled) return null;

        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < config.cache.ttl) {
            return cached.data;
        }

        // 清理过期缓存
        if (cached) {
            this.cache.delete(key);
        }

        return null;
    }

    /**
     * 保存到缓存
     */
    saveToCache(key, data) {
        if (!config.cache.enabled) return;

        // 检查缓存大小
        if (this.cache.size >= config.cache.maxSize) {
            // 删除最旧的条目
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }

        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    /**
     * 转换LaTeX到SVG
     */
    async convertToSVG(latex, options = {}) {
        return new Promise((resolve, reject) => {
            const mathOptions = {
                math: latex,
                format: "TeX",
                svg: true,
                ...options
            };

            mathjax.typeset(mathOptions, (data) => {
                if (data.errors) {
                    reject(new Error(`SVG转换失败: ${data.errors.join(', ')}`));
                } else {
                    resolve({
                        svg: data.svg,
                        width: data.width,
                        height: data.height
                    });
                }
            });
        });
    }

    /**
     * 转换LaTeX到MathML
     */
    async convertToMathML(latex, options = {}) {
        return new Promise((resolve, reject) => {
            const mathOptions = {
                math: latex,
                format: "TeX",
                mml: true,
                ...options
            };

            mathjax.typeset(mathOptions, (data) => {
                if (data.errors) {
                    reject(new Error(`MathML转换失败: ${data.errors.join(', ')}`));
                } else {
                    resolve({
                        mathml: data.mml,
                        width: data.width,
                        height: data.height
                    });
                }
            });
        });
    }

    /**
     * 转换LaTeX到PNG
     */
    async convertToPNG(latex, options = {}) {
        try {
            // 先转换为SVG
            const svgResult = await this.convertToSVG(latex, options);
            
            // 使用sharp将SVG转换为PNG
            const pngBuffer = await sharp(Buffer.from(svgResult.svg))
                .png({
                    quality: config.png.quality,
                    compressionLevel: 6
                })
                .resize({
                    width: options.width || config.png.width,
                    height: options.height || config.png.height,
                    fit: 'inside',
                    withoutEnlargement: true
                })
                .toBuffer();

            return {
                png: pngBuffer.toString('base64'),
                width: svgResult.width,
                height: svgResult.height,
                mimeType: 'image/png'
            };

        } catch (error) {
            throw new Error(`PNG转换失败: ${error.message}`);
        }
    }

    /**
     * 主转换方法
     */
    async convert(latex, format = 'svg', options = {}) {
        try {
            // 验证输入
            this.validateLatex(latex);

            // 检查缓存
            const cacheKey = this.getCacheKey(latex, format, options);
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                this.logger.info(`缓存命中: ${latex.substring(0, 50)}...`);
                return cached;
            }

            let result;

            // 根据格式执行转换
            switch (format.toLowerCase()) {
                case 'svg':
                    result = await this.convertToSVG(latex, options);
                    break;
                
                case 'mathml':
                case 'mml':
                    result = await this.convertToMathML(latex, options);
                    break;
                
                case 'png':
                    result = await this.convertToPNG(latex, options);
                    break;
                
                case 'all':
                    const [svgRes, mmlRes, pngRes] = await Promise.all([
                        this.convertToSVG(latex, options),
                        this.convertToMathML(latex, options),
                        this.convertToPNG(latex, options)
                    ]);
                    
                    result = {
                        svg: svgRes.svg,
                        mathml: mmlRes.mathml,
                        png: pngRes.png,
                        width: svgRes.width,
                        height: svgRes.height
                    };
                    break;
                
                default:
                    throw new Error(`不支持的格式: ${format}`);
            }

            // 添加元数据
            result.latex = latex;
            result.format = format;
            result.timestamp = new Date().toISOString();

            // 保存到缓存
            this.saveToCache(cacheKey, result);

            this.logger.info(`转换成功: ${latex.substring(0, 50)}... -> ${format}`);
            return result;

        } catch (error) {
            this.logger.error(`转换失败: ${error.message}, LaTeX: ${latex}`);
            throw error;
        }
    }

    /**
     * 批量转换
     */
    async batchConvert(formulas, format = 'svg', options = {}) {
        try {
            if (formulas.length > config.security.maxBatchSize) {
                throw new Error(`批量转换数量超过限制: ${config.security.maxBatchSize}`);
            }

            this.logger.info(`开始批量转换: ${formulas.length}个公式`);

            // 并发转换，但限制并发数
            const results = [];
            const concurrency = Math.min(formulas.length, config.performance.maxConcurrent);
            
            for (let i = 0; i < formulas.length; i += concurrency) {
                const batch = formulas.slice(i, i + concurrency);
                const batchPromises = batch.map(async (formula, index) => {
                    try {
                        const latex = typeof formula === 'string' ? formula : formula.latex;
                        const formulaOptions = typeof formula === 'object' ? { ...options, ...formula.options } : options;
                        
                        const result = await this.convert(latex, format, formulaOptions);
                        return {
                            index: i + index,
                            success: true,
                            data: result
                        };
                    } catch (error) {
                        return {
                            index: i + index,
                            success: false,
                            error: error.message,
                            latex: typeof formula === 'string' ? formula : formula.latex
                        };
                    }
                });

                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);
            }

            // 按原始顺序排序
            results.sort((a, b) => a.index - b.index);

            const successCount = results.filter(r => r.success).length;
            this.logger.info(`批量转换完成: ${successCount}/${formulas.length}成功`);

            return results;

        } catch (error) {
            this.logger.error(`批量转换失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 清理缓存
     */
    clearCache() {
        this.cache.clear();
        this.logger.info('缓存已清理');
    }

    /**
     * 获取缓存统计
     */
    getCacheStats() {
        return {
            size: this.cache.size,
            maxSize: config.cache.maxSize,
            enabled: config.cache.enabled,
            ttl: config.cache.ttl
        };
    }
}

module.exports = LatexConverter;
