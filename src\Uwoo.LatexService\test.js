/**
 * Uwoo LaTeX服务测试文件
 */

const axios = require('axios');

// 测试配置
const BASE_URL = 'http://localhost:3001';
const TEST_FORMULAS = [
    {
        name: '简单分数',
        latex: '\\frac{a}{b}',
        expected: 'svg'
    },
    {
        name: '二次公式',
        latex: 'x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}',
        expected: 'svg'
    },
    {
        name: '积分',
        latex: '\\int_{0}^{\\infty} e^{-x} dx = 1',
        expected: 'svg'
    },
    {
        name: '矩阵',
        latex: '\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}',
        expected: 'svg'
    },
    {
        name: '求和',
        latex: '\\sum_{i=1}^{n} i = \\frac{n(n+1)}{2}',
        expected: 'svg'
    },
    {
        name: '希腊字母',
        latex: '\\alpha + \\beta = \\gamma',
        expected: 'svg'
    }
];

/**
 * 测试健康检查
 */
async function testHealth() {
    console.log('\n🔍 测试健康检查...');
    try {
        const response = await axios.get(`${BASE_URL}/health`);
        console.log('✅ 健康检查通过:', response.data);
        return true;
    } catch (error) {
        console.error('❌ 健康检查失败:', error.message);
        return false;
    }
}

/**
 * 测试单个公式转换
 */
async function testSingleConvert() {
    console.log('\n🔍 测试单个公式转换...');
    
    for (const formula of TEST_FORMULAS) {
        try {
            console.log(`\n测试: ${formula.name}`);
            console.log(`LaTeX: ${formula.latex}`);
            
            const response = await axios.post(`${BASE_URL}/api/convert`, {
                latex: formula.latex,
                format: 'svg'
            });
            
            if (response.data.success) {
                console.log('✅ 转换成功');
                console.log(`SVG长度: ${response.data.data.svg ? response.data.data.svg.length : 0}`);
                console.log(`尺寸: ${response.data.data.width} x ${response.data.data.height}`);
            } else {
                console.log('❌ 转换失败:', response.data.error);
            }
            
        } catch (error) {
            console.error(`❌ ${formula.name} 转换异常:`, error.response?.data || error.message);
        }
    }
}

/**
 * 测试多格式转换
 */
async function testMultiFormat() {
    console.log('\n🔍 测试多格式转换...');
    
    const latex = '\\frac{x^2 + y^2}{z}';
    const formats = ['svg', 'mathml', 'png', 'all'];
    
    for (const format of formats) {
        try {
            console.log(`\n测试格式: ${format}`);
            
            const response = await axios.post(`${BASE_URL}/api/convert`, {
                latex: latex,
                format: format
            });
            
            if (response.data.success) {
                console.log('✅ 转换成功');
                const data = response.data.data;
                
                if (data.svg) console.log(`SVG: ${data.svg.substring(0, 100)}...`);
                if (data.mathml) console.log(`MathML: ${data.mathml.substring(0, 100)}...`);
                if (data.png) console.log(`PNG: ${data.png.substring(0, 50)}... (base64)`);
                
            } else {
                console.log('❌ 转换失败:', response.data.error);
            }
            
        } catch (error) {
            console.error(`❌ ${format} 格式转换异常:`, error.response?.data || error.message);
        }
    }
}

/**
 * 测试批量转换
 */
async function testBatchConvert() {
    console.log('\n🔍 测试批量转换...');
    
    try {
        const formulas = TEST_FORMULAS.slice(0, 3).map(f => ({ latex: f.latex }));
        
        const response = await axios.post(`${BASE_URL}/api/batch-convert`, {
            formulas: formulas,
            format: 'svg'
        });
        
        if (response.data.success) {
            console.log('✅ 批量转换成功');
            console.log(`处理数量: ${response.data.count}`);
            
            response.data.data.forEach((result, index) => {
                if (result.success) {
                    console.log(`  ${index + 1}. ✅ ${formulas[index].latex.substring(0, 30)}...`);
                } else {
                    console.log(`  ${index + 1}. ❌ ${result.error}`);
                }
            });
        } else {
            console.log('❌ 批量转换失败:', response.data.error);
        }
        
    } catch (error) {
        console.error('❌ 批量转换异常:', error.response?.data || error.message);
    }
}

/**
 * 测试错误处理
 */
async function testErrorHandling() {
    console.log('\n🔍 测试错误处理...');
    
    const errorTests = [
        {
            name: '空LaTeX',
            data: { latex: '', format: 'svg' },
            expectedError: true
        },
        {
            name: '无效格式',
            data: { latex: 'x^2', format: 'invalid' },
            expectedError: true
        },
        {
            name: '超长LaTeX',
            data: { latex: 'x'.repeat(20000), format: 'svg' },
            expectedError: true
        }
    ];
    
    for (const test of errorTests) {
        try {
            console.log(`\n测试: ${test.name}`);
            
            const response = await axios.post(`${BASE_URL}/api/convert`, test.data);
            
            if (test.expectedError && response.data.success) {
                console.log('❌ 应该失败但成功了');
            } else if (!test.expectedError && !response.data.success) {
                console.log('❌ 应该成功但失败了');
            } else {
                console.log('✅ 错误处理正确');
            }
            
        } catch (error) {
            if (test.expectedError) {
                console.log('✅ 正确捕获错误:', error.response?.data?.error || error.message);
            } else {
                console.log('❌ 意外错误:', error.response?.data || error.message);
            }
        }
    }
}

/**
 * 测试支持的语法查询
 */
async function testSupportedSyntax() {
    console.log('\n🔍 测试支持的语法查询...');
    
    try {
        const response = await axios.get(`${BASE_URL}/api/supported-syntax`);
        
        if (response.data.success) {
            console.log('✅ 语法查询成功');
            console.log('支持的语法:');
            Object.entries(response.data.data).forEach(([category, items]) => {
                console.log(`  ${category}:`);
                items.forEach(item => console.log(`    - ${item}`));
            });
        } else {
            console.log('❌ 语法查询失败');
        }
        
    } catch (error) {
        console.error('❌ 语法查询异常:', error.response?.data || error.message);
    }
}

/**
 * 主测试函数
 */
async function runTests() {
    console.log('🚀 开始Uwoo LaTeX服务测试');
    console.log(`📍 测试地址: ${BASE_URL}`);
    
    // 等待服务启动
    console.log('\n⏳ 等待服务启动...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 执行测试
    const healthOk = await testHealth();
    if (!healthOk) {
        console.log('\n❌ 服务未启动，请先运行: npm start');
        return;
    }
    
    await testSingleConvert();
    await testMultiFormat();
    await testBatchConvert();
    await testErrorHandling();
    await testSupportedSyntax();
    
    console.log('\n🎉 测试完成!');
}

// 运行测试
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = {
    runTests,
    testHealth,
    testSingleConvert,
    testMultiFormat,
    testBatchConvert,
    testErrorHandling,
    testSupportedSyntax
};
