@echo off
echo 🚀 Uwoo LaTeX转换微服务安装脚本
echo =====================================
echo.

REM 检查Node.js是否安装
echo 🔍 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Node.js
    echo.
    echo 📥 请先安装Node.js 16或更高版本:
    echo    https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js版本:
node --version

REM 检查npm是否可用
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: npm不可用
    pause
    exit /b 1
)

echo ✅ npm版本:
npm --version
echo.

REM 安装依赖
echo 📦 安装项目依赖...
echo 这可能需要几分钟时间，请耐心等待...
echo.

npm install
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    echo.
    echo 💡 可能的解决方案:
    echo    1. 检查网络连接
    echo    2. 尝试使用淘宝镜像: npm config set registry https://registry.npmmirror.com
    echo    3. 清理缓存: npm cache clean --force
    echo.
    pause
    exit /b 1
)

echo ✅ 依赖安装完成!
echo.

REM 创建日志目录
if not exist "logs" (
    mkdir logs
    echo ✅ 创建日志目录: logs/
)

REM 运行测试
echo 🧪 运行基础测试...
echo.

REM 启动服务（后台）
echo 🚀 启动测试服务...
start /B npm start
echo 等待服务启动...
timeout /t 5 /nobreak >nul

REM 运行测试
node test.js
set TEST_RESULT=%errorlevel%

REM 停止测试服务
echo.
echo 🛑 停止测试服务...
taskkill /f /im node.exe >nul 2>&1

if %TEST_RESULT% equ 0 (
    echo.
    echo 🎉 安装和测试完成!
    echo.
    echo 📋 使用说明:
    echo    启动服务: npm start 或 start.bat
    echo    开发模式: npm run dev
    echo    运行测试: npm test
    echo.
    echo 📍 服务地址: http://localhost:3001
    echo 📊 健康检查: http://localhost:3001/health
    echo 📖 API文档: 查看README.md
    echo.
) else (
    echo.
    echo ⚠️  安装完成，但测试失败
    echo 请检查错误信息并手动测试服务
    echo.
)

echo 按任意键退出...
pause >nul
