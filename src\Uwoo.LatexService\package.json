{"name": "uwoo-latex-service", "version": "1.0.0", "description": "LaTeX数学公式转换微服务 - 支持LaTeX到SVG/MathML/PNG转换", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test.js"}, "keywords": ["latex", "math", "formula", "mathjax", "svg", "mathml", "microservice"], "author": "Uwoo Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "mathjax-node": "^2.1.1", "mathjax-node-svg2png": "^1.2.1", "sharp": "^0.32.6", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}