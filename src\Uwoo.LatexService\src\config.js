/**
 * Uwoo LaTeX服务配置文件
 */

module.exports = {
    // 服务器配置
    port: process.env.PORT || 3001,
    host: process.env.HOST || '0.0.0.0',
    
    // MathJax配置
    mathjax: {
        // 输入格式配置
        tex: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$'], ['\\[', '\\]']],
            processEscapes: true,
            processEnvironments: true,
            packages: ['base', 'ams', 'newcommand', 'configmacros', 'action']
        },
        
        // SVG输出配置
        svg: {
            scale: 1.2,
            minScale: 0.5,
            mtextInheritFont: false,
            merrorInheritFont: true,
            mathmlSpacing: false,
            skipAttributes: {},
            exFactor: 0.5,
            displayAlign: 'center',
            displayIndent: '0',
            fontCache: 'local',
            localID: null,
            internalSpeechTitles: true,
            titleID: 0
        },
        
        // MathML输出配置
        mml: {
            semantics: false,
            useLabels: true,
            forceReparse: false
        },
        
        // 通用配置
        loader: {
            load: ['input/tex', 'output/svg', 'output/mml']
        },
        
        startup: {
            typeset: false
        }
    },
    
    // PNG转换配置
    png: {
        width: 1200,
        height: 600,
        density: 144,
        background: 'white',
        quality: 90
    },
    
    // 缓存配置
    cache: {
        enabled: true,
        maxSize: 1000, // 最大缓存条目数
        ttl: 3600000   // 缓存时间：1小时（毫秒）
    },
    
    // 安全配置
    security: {
        maxLatexLength: 10000,
        maxBatchSize: 100,
        allowedCommands: [
            // 基础数学
            'frac', 'sqrt', 'sum', 'int', 'lim', 'prod',
            // 上下标
            '^', '_',
            // 希腊字母
            'alpha', 'beta', 'gamma', 'delta', 'epsilon', 'zeta', 'eta', 'theta',
            'iota', 'kappa', 'lambda', 'mu', 'nu', 'xi', 'pi', 'rho', 'sigma',
            'tau', 'upsilon', 'phi', 'chi', 'psi', 'omega',
            'Alpha', 'Beta', 'Gamma', 'Delta', 'Epsilon', 'Zeta', 'Eta', 'Theta',
            'Iota', 'Kappa', 'Lambda', 'Mu', 'Nu', 'Xi', 'Pi', 'Rho', 'Sigma',
            'Tau', 'Upsilon', 'Phi', 'Chi', 'Psi', 'Omega',
            // 运算符
            'pm', 'mp', 'times', 'div', 'cdot', 'ast', 'star', 'circ', 'bullet',
            'oplus', 'ominus', 'otimes', 'oslash', 'odot',
            // 关系符
            'leq', 'geq', 'neq', 'approx', 'equiv', 'sim', 'simeq', 'cong',
            'propto', 'parallel', 'perp', 'subset', 'supset', 'subseteq', 'supseteq',
            'in', 'ni', 'notin', 'cup', 'cap', 'setminus',
            // 箭头
            'leftarrow', 'rightarrow', 'leftrightarrow', 'Leftarrow', 'Rightarrow',
            'Leftrightarrow', 'uparrow', 'downarrow', 'updownarrow',
            // 特殊符号
            'infty', 'partial', 'nabla', 'exists', 'forall', 'emptyset',
            'angle', 'triangle', 'square', 'diamond', 'clubsuit', 'spadesuit',
            // 环境
            'begin', 'end', 'matrix', 'pmatrix', 'bmatrix', 'vmatrix', 'Vmatrix',
            'cases', 'align', 'aligned', 'split', 'gather', 'gathered',
            // 字体
            'mathbb', 'mathbf', 'mathcal', 'mathfrak', 'mathit', 'mathrm', 'mathsf', 'mathtt',
            'boldsymbol', 'text', 'textbf', 'textit', 'textrm', 'textsf', 'texttt',
            // 空格和格式
            'quad', 'qquad', 'hspace', 'vspace', 'phantom', 'hphantom', 'vphantom',
            'left', 'right', 'big', 'Big', 'bigg', 'Bigg',
            // 其他常用
            'overline', 'underline', 'overbrace', 'underbrace', 'vec', 'hat', 'tilde',
            'bar', 'dot', 'ddot', 'check', 'grave', 'acute', 'breve'
        ],
        
        // 禁用的危险命令
        blockedCommands: [
            'input', 'include', 'write', 'read', 'openin', 'openout',
            'immediate', 'special', 'pdfobj', 'pdfliteral'
        ]
    },
    
    // 日志配置
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        file: {
            enabled: true,
            path: './logs',
            maxSize: '10m',
            maxFiles: '7d'
        },
        console: {
            enabled: true,
            colorize: true
        }
    },
    
    // 性能配置
    performance: {
        timeout: 30000, // 30秒超时
        maxConcurrent: 10, // 最大并发转换数
        memoryLimit: '512mb'
    }
};
