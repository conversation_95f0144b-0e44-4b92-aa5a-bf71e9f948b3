/**
 * Uwoo LaTeX转换微服务
 * 提供LaTeX数学公式到SVG/MathML/PNG的转换服务
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const winston = require('winston');

// 导入转换器模块
const LatexConverter = require('./src/converter');
const config = require('./src/config');

// 创建Express应用
const app = express();

// 配置日志
const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
    ),
    defaultMeta: { service: 'uwoo-latex-service' },
    transports: [
        new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
        new winston.transports.File({ filename: 'logs/combined.log' }),
        new winston.transports.Console({
            format: winston.format.simple()
        })
    ]
});

// 中间件配置
app.use(helmet()); // 安全头
app.use(cors()); // 跨域支持
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } })); // 请求日志
app.use(express.json({ limit: '10mb' })); // JSON解析，限制10MB
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 速率限制
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 1000, // 限制每个IP 15分钟内最多1000个请求
    message: {
        error: '请求过于频繁，请稍后再试',
        retryAfter: '15分钟'
    }
});
app.use('/api/', limiter);

// 创建转换器实例
const converter = new LatexConverter(logger);

// 健康检查端点
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'uwoo-latex-service',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// LaTeX转换端点
app.post('/api/convert',
    // 输入验证
    [
        body('latex')
            .notEmpty()
            .withMessage('LaTeX代码不能为空')
            .isLength({ max: 10000 })
            .withMessage('LaTeX代码长度不能超过10000字符'),
        body('format')
            .optional()
            .isIn(['svg', 'mathml', 'png', 'all'])
            .withMessage('格式必须是: svg, mathml, png, all'),
        body('options')
            .optional()
            .isObject()
            .withMessage('选项必须是对象')
    ],
    async (req, res) => {
        try {
            // 检查验证结果
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const { latex, format = 'svg', options = {} } = req.body;
            
            logger.info(`转换请求: ${latex.substring(0, 100)}${latex.length > 100 ? '...' : ''}`);

            // 执行转换
            const result = await converter.convert(latex, format, options);

            res.json({
                success: true,
                data: result,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            logger.error('转换失败:', error);
            
            res.status(500).json({
                success: false,
                error: '转换失败',
                message: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }
);

// 批量转换端点
app.post('/api/batch-convert',
    [
        body('formulas')
            .isArray({ min: 1, max: 100 })
            .withMessage('公式数组长度必须在1-100之间'),
        body('formulas.*.latex')
            .notEmpty()
            .withMessage('每个公式的LaTeX代码不能为空'),
        body('format')
            .optional()
            .isIn(['svg', 'mathml', 'png'])
            .withMessage('格式必须是: svg, mathml, png')
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const { formulas, format = 'svg', options = {} } = req.body;
            
            logger.info(`批量转换请求: ${formulas.length}个公式`);

            // 批量转换
            const results = await converter.batchConvert(formulas, format, options);

            res.json({
                success: true,
                data: results,
                count: results.length,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            logger.error('批量转换失败:', error);
            
            res.status(500).json({
                success: false,
                error: '批量转换失败',
                message: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }
);

// 支持的LaTeX语法查询端点
app.get('/api/supported-syntax', (req, res) => {
    res.json({
        success: true,
        data: {
            basic: ['分数: \\frac{a}{b}', '根号: \\sqrt{x}', '上标: x^2', '下标: x_1'],
            advanced: ['积分: \\int', '求和: \\sum', '极限: \\lim', '矩阵: \\begin{matrix}'],
            symbols: ['希腊字母: \\alpha, \\beta, \\gamma', '运算符: \\pm, \\times, \\div'],
            environments: ['对齐: \\begin{align}', '分段: \\begin{cases}']
        }
    });
});

// 错误处理中间件
app.use((error, req, res, next) => {
    logger.error('未处理的错误:', error);
    
    res.status(500).json({
        success: false,
        error: '服务器内部错误',
        message: process.env.NODE_ENV === 'development' ? error.message : '请联系管理员'
    });
});

// 404处理
app.use((req, res) => {
    res.status(404).json({
        success: false,
        error: '接口不存在',
        path: req.path
    });
});

// 启动服务器
const PORT = process.env.PORT || config.port || 3001;
const HOST = process.env.HOST || config.host || '0.0.0.0';

app.listen(PORT, HOST, () => {
    logger.info(`🚀 Uwoo LaTeX服务启动成功!`);
    logger.info(`📍 地址: http://${HOST}:${PORT}`);
    logger.info(`🔧 环境: ${process.env.NODE_ENV || 'development'}`);
    logger.info(`📊 健康检查: http://${HOST}:${PORT}/health`);
});

// 优雅关闭
process.on('SIGTERM', () => {
    logger.info('收到SIGTERM信号，正在关闭服务器...');
    process.exit(0);
});

process.on('SIGINT', () => {
    logger.info('收到SIGINT信号，正在关闭服务器...');
    process.exit(0);
});
