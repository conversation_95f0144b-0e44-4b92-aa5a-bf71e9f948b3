@echo off
echo 🚀 启动Uwoo LaTeX转换微服务...
echo.

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js 16+
    echo 📥 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js版本:
node --version

REM 检查npm是否可用
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: npm不可用
    pause
    exit /b 1
)

echo ✅ npm版本:
npm --version
echo.

REM 检查是否已安装依赖
if not exist "node_modules" (
    echo 📦 首次运行，正在安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
    echo.
)

REM 创建日志目录
if not exist "logs" mkdir logs

REM 启动服务
echo 🎯 启动服务...
echo 📍 地址: http://localhost:3001
echo 📊 健康检查: http://localhost:3001/health
echo 📖 API文档: 查看README.md
echo.
echo 💡 提示: 按Ctrl+C停止服务
echo.

npm start
