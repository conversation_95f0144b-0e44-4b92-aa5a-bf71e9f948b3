# Uwoo LaTeX转换微服务

🚀 专业的LaTeX数学公式转换微服务，支持LaTeX到SVG/MathML/PNG的高质量转换。

## ✨ 特性

- 🎯 **完整LaTeX支持** - 支持分数、根号、积分、矩阵、希腊字母等
- 🖼️ **多格式输出** - SVG、MathML、PNG格式
- ⚡ **高性能** - 内置缓存、批量处理、并发控制
- 🔒 **安全可靠** - 输入验证、速率限制、错误处理
- 📊 **生产就绪** - 日志记录、健康检查、优雅关闭

## 🛠️ 安装

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
cd src/Uwoo.LatexService
npm install
```

## 🚀 启动服务

### 开发模式
```bash
npm run dev
```

### 生产模式
```bash
npm start
```

服务将在 `http://localhost:3001` 启动

## 📡 API接口

### 健康检查
```http
GET /health
```

**响应示例：**
```json
{
  "status": "healthy",
  "service": "uwoo-latex-service",
  "version": "1.0.0",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 123.456
}
```

### 单个公式转换
```http
POST /api/convert
Content-Type: application/json

{
  "latex": "\\frac{a}{b}",
  "format": "svg",
  "options": {}
}
```

**参数说明：**
- `latex` (必需): LaTeX公式代码
- `format` (可选): 输出格式 `svg|mathml|png|all`，默认 `svg`
- `options` (可选): 转换选项

**响应示例：**
```json
{
  "success": true,
  "data": {
    "svg": "<svg>...</svg>",
    "width": "2.5ex",
    "height": "2.0ex",
    "latex": "\\frac{a}{b}",
    "format": "svg",
    "timestamp": "2024-01-01T00:00:00.000Z"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 批量转换
```http
POST /api/batch-convert
Content-Type: application/json

{
  "formulas": [
    {"latex": "x^2"},
    {"latex": "\\sqrt{y}"}
  ],
  "format": "svg"
}
```

### 支持的语法查询
```http
GET /api/supported-syntax
```

## 🧪 测试

### 运行测试
```bash
npm test
```

### 手动测试
```bash
# 启动服务
npm start

# 在另一个终端运行测试
node test.js
```

## 📝 支持的LaTeX语法

### 基础数学
- 分数: `\frac{a}{b}`
- 根号: `\sqrt{x}`, `\sqrt[n]{x}`
- 上标: `x^2`, `x^{n+1}`
- 下标: `x_1`, `x_{i,j}`

### 运算符
- 基本: `+`, `-`, `\times`, `\div`, `\pm`, `\mp`
- 关系: `=`, `\neq`, `\leq`, `\geq`, `\approx`
- 集合: `\in`, `\subset`, `\cup`, `\cap`

### 希腊字母
- 小写: `\alpha`, `\beta`, `\gamma`, `\delta`, `\pi`, `\theta`
- 大写: `\Alpha`, `\Beta`, `\Gamma`, `\Delta`, `\Pi`, `\Theta`

### 高级结构
- 积分: `\int`, `\iint`, `\iiint`
- 求和: `\sum_{i=1}^{n}`
- 极限: `\lim_{x \to 0}`
- 矩阵: `\begin{matrix}...\end{matrix}`

### 环境
- 对齐: `\begin{align}...\end{align}`
- 分段: `\begin{cases}...\end{cases}`
- 矩阵: `\begin{pmatrix}...\end{pmatrix}`

## ⚙️ 配置

主要配置在 `src/config.js` 中：

```javascript
module.exports = {
  port: 3001,
  mathjax: { /* MathJax配置 */ },
  cache: {
    enabled: true,
    maxSize: 1000,
    ttl: 3600000
  },
  security: {
    maxLatexLength: 10000,
    maxBatchSize: 100
  }
};
```

## 🔧 与C#集成

在Uwoo.Tools中集成使用：

```csharp
public class LatexService
{
    private readonly HttpClient _httpClient;
    private readonly string _serviceUrl = "http://localhost:3001";

    public async Task<string> ConvertLatexToSVG(string latex)
    {
        var request = new { latex = latex, format = "svg" };
        var json = JsonConvert.SerializeObject(request);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        
        var response = await _httpClient.PostAsync($"{_serviceUrl}/api/convert", content);
        var result = await response.Content.ReadAsStringAsync();
        
        var data = JsonConvert.DeserializeObject<dynamic>(result);
        return data.success ? data.data.svg : null;
    }
}
```

## 📊 性能

- **缓存命中率**: ~90% (典型场景)
- **转换速度**: ~50ms/公式 (简单公式)
- **并发处理**: 最多10个并发转换
- **内存使用**: ~100MB (基础运行)

## 🐳 Docker部署

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3001
CMD ["npm", "start"]
```

## 📋 日志

日志文件位置：
- 错误日志: `logs/error.log`
- 综合日志: `logs/combined.log`
- 控制台输出: 实时显示

## 🔍 监控

### 健康检查
```bash
curl http://localhost:3001/health
```

### 缓存统计
通过日志查看缓存命中率和使用情况。

## 🤝 贡献

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 🆘 故障排除

### 常见问题

1. **服务启动失败**
   - 检查Node.js版本 >= 16
   - 确保端口3001未被占用
   - 查看错误日志

2. **转换失败**
   - 检查LaTeX语法是否正确
   - 查看是否包含不支持的命令
   - 检查输入长度限制

3. **性能问题**
   - 启用缓存
   - 调整并发数量
   - 监控内存使用

### 联系支持

如有问题，请创建Issue或联系开发团队。
